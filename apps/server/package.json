{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsc && tsc-alias", "check-types": "tsc --noEmit", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "bun run --hot src/index.ts", "start": "bun run dist/src/index.js", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"dotenv": "^16.4.7", "zod": "^3.25.16", "@trpc/server": "^11.4.2", "@trpc/client": "^11.4.2", "@hono/trpc-server": "^0.4.0", "hono": "^4.8.2", "drizzle-orm": "^0.44.2", "pg": "^8.14.1", "better-auth": "^1.2.10"}, "trustedDependencies": ["supabase"], "devDependencies": {"tsc-alias": "^1.8.11", "typescript": "^5.8.2", "@types/bun": "^1.2.6", "drizzle-kit": "^0.31.2", "@types/pg": "^8.11.11"}}