import { redirect } from "@tanstack/react-router";
import { authClient } from "./auth-client";

export async function requireAuth() {
	try {
		// Get the current session
		const session = await authClient.$fetch("/session", {
			method: "GET",
		});

		if (!session) {
			throw redirect({
				to: "/login",
				search: {
					redirect: window.location.pathname,
				},
			});
		}

		return session;
	} catch {
		// If there's an error fetching the session, redirect to login
		throw redirect({
			to: "/login",
			search: {
				redirect: window.location.pathname,
			},
		});
	}
}

export async function requireGuest() {
	try {
		// Get the current session
		const session = await authClient.$fetch("/session", {
			method: "GET",
		});

		if (session) {
			throw redirect({
				to: "/dashboard",
			});
		}

		return null;
	} catch {
		// If there's an error, assume no session and allow access
		return null;
	}
}
