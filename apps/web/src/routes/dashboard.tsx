import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { authClient } from "@/lib/auth-client";
import { requireAuth } from "@/lib/auth-guards";
import { trpc } from "@/utils/trpc";

export const Route = createFileRoute("/dashboard")({
	beforeLoad: async () => {
		return await requireAuth();
	},
	component: RouteComponent,
});

function RouteComponent() {
	const { data: session, isPending } = authClient.useSession();
	const privateData = useQuery(trpc.privateData.queryOptions());

	if (isPending) {
		return <div>Loading...</div>;
	}

	return (
		<div>
			<h1>Dashboard</h1>
			<p>Welcome {session?.user.name}</p>
			<p>privateData: {privateData.data?.message}</p>
		</div>
	);
}
