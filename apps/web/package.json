{"name": "web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port=3001", "build": "vite build", "serve": "vite preview", "start": "vite", "check-types": "tsc --noEmit", "generate-pwa-assets": "pwa-assets-generator"}, "devDependencies": {"@tanstack/react-router-devtools": "^1.114.27", "@tanstack/router-plugin": "^1.114.27", "@types/node": "^22.13.13", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "postcss": "^8.5.3", "tailwindcss": "^4.0.15", "vite": "^6.2.2", "@tanstack/react-query-devtools": "^5.80.5", "@vite-pwa/assets-generator": "^1.0.0"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tailwindcss/vite": "^4.0.15", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.80.5", "@tanstack/react-router": "^1.114.25", "@trpc/client": "^11.4.2", "@trpc/server": "^11.4.2", "@trpc/tanstack-react-query": "^11.4.2", "better-auth": "^1.2.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.473.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "sharp": "^0.34.2", "sharp-ico": "^0.1.5", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.2.5", "vite-plugin-pwa": "^0.21.2", "zod": "^3.25.16"}}