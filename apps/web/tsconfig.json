{"extends": "../../tooling/typescript/react.json", "compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "verbatimModuleSyntax": true, "noEmit": true, "types": ["vite/client"], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@moneta/database": ["../../packages/database/src"], "@moneta/database/*": ["../../packages/database/src/*"], "@moneta/auth": ["../../packages/auth/src"], "@moneta/auth/*": ["../../packages/auth/src/*"], "@moneta/ui": ["../../packages/ui/src"], "@moneta/ui/*": ["../../packages/ui/src/*"], "@moneta/utils": ["../../packages/utils/src"], "@moneta/utils/*": ["../../packages/utils/src/*"], "@moneta/api-client": ["../../packages/api-client/src"], "@moneta/api-client/*": ["../../packages/api-client/src/*"], "@moneta/integrations": ["../../packages/integrations/src"], "@moneta/integrations/*": ["../../packages/integrations/src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules"], "references": [{"path": "../api"}, {"path": "../../packages/database"}, {"path": "../../packages/auth"}, {"path": "../../packages/ui"}, {"path": "../../packages/utils"}, {"path": "../../packages/api-client"}, {"path": "../../packages/integrations"}]}