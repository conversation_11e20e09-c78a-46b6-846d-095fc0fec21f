{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/lib/auth.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AACzC,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAC9D,OAAO,EAAE,EAAE,EAAE,MAAM,OAAO,CAAC;AAC3B,OAAO,KAAK,MAAM,MAAM,mBAAmB,CAAC;AAE5C,MAAM,CAAC,MAAM,IAAI,GAAG,UAAU,CAAC;IAC9B,QAAQ,EAAE,cAAc,CAAC,EAAE,EAAE;QAC5B,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,MAAM;KACd,CAAC;IACF,cAAc,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE,CAAC;IAC/C,gBAAgB,EAAE;QACjB,OAAO,EAAE,IAAI;KACb;IACD,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB;IACtC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe;IACpC,OAAO,EAAE;QACR,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,SAAS;QACtC,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,QAAQ;QACjC,WAAW,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,EAAE,GAAG,CAAC,EAAE,YAAY;SAC5B;KACD;IACD,OAAO,EAAE;QACR,YAAY,EAAE;YACb,IAAI,EAAE,2BAA2B;YACjC,OAAO,EAAE;gBACR,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,GAAG;gBACT,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;aAC7C;SACD;KACD;CACD,CAAC,CAAC"}