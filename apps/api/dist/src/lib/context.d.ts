import type { Context as Hono<PERSON>ontext } from "hono";
export type CreateContextOptions = {
    context: HonoContext;
};
export declare function createContext({ context }: CreateContextOptions): Promise<{
    session: {
        session: {
            id: string;
            token: string;
            userId: string;
            expiresAt: Date;
            createdAt: Date;
            updatedAt: Date;
            ipAddress?: string | null | undefined | undefined | undefined;
            userAgent?: string | null | undefined | undefined | undefined;
        };
        user: {
            id: string;
            name: string;
            emailVerified: boolean;
            email: string;
            createdAt: Date;
            updatedAt: Date;
            image?: string | null | undefined | undefined | undefined;
        };
    } | null;
}>;
export type Context = Awaited<ReturnType<typeof createContext>>;
//# sourceMappingURL=context.d.ts.map