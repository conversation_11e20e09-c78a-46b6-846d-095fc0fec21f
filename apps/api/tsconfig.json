{"extends": "../../tooling/typescript/base.json", "compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "verbatimModuleSyntax": true, "noEmit": false, "outDir": "./dist", "baseUrl": "./", "paths": {"@/*": ["./src/*"], "@moneta/database": ["../../packages/database/src"], "@moneta/database/*": ["../../packages/database/src/*"], "@moneta/auth": ["../../packages/auth/src"], "@moneta/auth/*": ["../../packages/auth/src/*"], "@moneta/utils": ["../../packages/utils/src"], "@moneta/utils/*": ["../../packages/utils/src/*"], "@moneta/integrations": ["../../packages/integrations/src"], "@moneta/integrations/*": ["../../packages/integrations/src/*"]}, "types": ["bun"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx"}, "include": ["src/**/*"], "exclude": ["node_modules"], "references": [{"path": "../../packages/database"}, {"path": "../../packages/auth"}, {"path": "../../packages/utils"}, {"path": "../../packages/integrations"}], "tsc-alias": {"resolveFullPaths": true}}