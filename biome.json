{"$schema": "https://biomejs.dev/schemas/2.0.5/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/.next", "!**/dist", "!**/.turbo", "!**/dev-dist", "!**/.zed", "!**/.vscode", "!**/routeTree.gen.ts", "!**/src-tauri", "!**/.nuxt", "!bts.jsonc"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedImports": {"fix": "safe", "level": "error"}, "noUnusedVariables": {"fix": "safe", "level": "error", "options": {"ignoreRestSiblings": true}}, "useExhaustiveDependencies": "info"}, "nursery": {"useSortedClasses": {"level": "warn", "fix": "safe", "options": {"functions": ["clsx", "cva", "cn"]}}}, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}, "suspicious": {"noDuplicateObjectKeys": "error"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}