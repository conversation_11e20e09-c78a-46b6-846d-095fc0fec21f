Project Name: moneta-finance
│  Frontend: tanstack-router
│  Backend: hono
│  Runtime: bun
│  API: trpc
│  Database: postgres
│  ORM: drizzle
│  Authentication: Yes
│  Addons: biome, husky, pwa, turborepo
│  Examples: none
│  Git Init: Yes
│  Package Manager: bun
│  Install Dependencies: Yes
│  Database Setup: supabase
│
│
●  Initializing Supabase project...
Generate VS Code settings for Deno? [y/N] y
Generated VS Code settings in .vscode/settings.json. Please install the recommended extension!
Finished supabase init.
│
◆  Supabase project initialized
│
●  Starting Supabase services (this may take a moment)...
Resolving dependencies
Resolved, downloaded and extracted [2]
Saved lockfile
supabase start is already running.
Stopped services: [supabase_imgproxy_server supabase_pooler_server]
supabase local development setup is running.

         API URL: http://127.0.0.1:54321
     GraphQL URL: http://127.0.0.1:54321/graphql/v1
  S3 Storage URL: http://127.0.0.1:54321/storage/v1/s3
          DB URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres
      Studio URL: http://127.0.0.1:54323
    Inbucket URL: http://127.0.0.1:54324
      JWT secret: super-secret-jwt-token-with-at-least-32-characters-long
        anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
service_role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
   S3 Access Key: 625729a08b95bf1b7ff351a663f3a23c
   S3 Secret Key: 850181e4652dd023b7a98c58ae0d2d34bd487ee0cc3254aed6eda37307425907
       S3 Region: local
│
◆  Supabase local development setup ready!
│
◆  Project template successfully scaffolded!
│

$ husky
◇  Dependencies installed successfully
