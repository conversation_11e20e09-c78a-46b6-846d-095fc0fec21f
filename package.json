{"name": "moneta-finance", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "prepare": "husky", "dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:api": "turbo -F api dev", "db:push": "turbo -F api db:push", "db:studio": "turbo -F api db:studio", "db:generate": "turbo -F api db:generate", "db:migrate": "turbo -F api db:migrate"}, "dependencies": {}, "devDependencies": {"turbo": "^2.5.4", "@biomejs/biome": "^2.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.0"}, "lint-staged": {"*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}": ["biome check --write ."]}, "packageManager": "bun@1.2.17"}