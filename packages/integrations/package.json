{"name": "@moneta/integrations", "version": "0.0.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./plaid": {"types": "./dist/plaid.d.ts", "import": "./dist/plaid.js"}, "./belvo": {"types": "./dist/belvo.d.ts", "import": "./dist/belvo.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit"}, "dependencies": {"@moneta/database": "workspace:*", "@moneta/utils": "workspace:*"}, "devDependencies": {"typescript": "^5.8.2"}}