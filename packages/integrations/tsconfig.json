{"extends": "../../tooling/typescript/base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "paths": {"@moneta/database": ["../database/src"], "@moneta/database/*": ["../database/src/*"], "@moneta/utils": ["../utils/src"], "@moneta/utils/*": ["../utils/src/*"]}}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"], "references": [{"path": "../database"}, {"path": "../utils"}]}