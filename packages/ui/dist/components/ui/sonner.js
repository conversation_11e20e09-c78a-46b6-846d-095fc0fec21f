"use client";
import { jsx as _jsx } from "react/jsx-runtime";
// TODO: Replace with proper theme hook
const useTheme = () => ({ theme: "system" });
import { Toaster as Sonner } from "sonner";
const Toaster = ({ ...props }) => {
    const { theme = "system" } = useTheme();
    return (_jsx(Sonner, { theme: theme ?? "system", className: "toaster group", style: {
            "--normal-bg": "var(--popover)",
            "--normal-text": "var(--popover-foreground)",
            "--normal-border": "var(--border)",
        }, ...props }));
};
export { Toaster };
//# sourceMappingURL=sonner.js.map