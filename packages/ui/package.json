{"name": "@moneta/ui", "version": "0.0.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./components/*": {"types": "./dist/components/*.d.ts", "import": "./dist/components/*.js"}, "./styles": {"import": "./dist/styles.css"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "tailwind-merge": "^2.6.0", "@moneta/utils": "workspace:*"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.2"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}