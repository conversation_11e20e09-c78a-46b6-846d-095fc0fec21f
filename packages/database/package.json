{"name": "@moneta/database", "version": "0.0.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./schemas/*": {"types": "./dist/schemas/*.d.ts", "import": "./dist/schemas/*.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"drizzle-orm": "^0.44.2", "pg": "^8.14.1"}, "devDependencies": {"@types/pg": "^8.11.11", "drizzle-kit": "^0.31.2", "typescript": "^5.8.2"}}