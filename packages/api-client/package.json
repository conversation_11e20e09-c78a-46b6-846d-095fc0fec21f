{"name": "@moneta/api-client", "version": "0.0.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./react": {"types": "./dist/react.d.ts", "import": "./dist/react.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit"}, "dependencies": {"@trpc/client": "^11.4.2", "@trpc/tanstack-react-query": "^11.4.2", "@tanstack/react-query": "^5.80.3", "react": "^19.0.0"}, "devDependencies": {"@types/react": "^19.1.8", "typescript": "^5.8.2"}, "peerDependencies": {"react": ">=18.0.0"}}