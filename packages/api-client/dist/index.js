import { QueryCache, QueryClient } from "@tanstack/react-query";
// Create a basic query client for now
export const queryClient = new QueryClient({
    queryCache: new QueryCache({
        onError: (error) => {
            console.error("Query error:", error.message);
        },
    }),
});
// TODO: Add tRPC client configuration when API router types are available
// For now, export a placeholder
export const trpc = {};
export const trpcClient = {};
//# sourceMappingURL=index.js.map