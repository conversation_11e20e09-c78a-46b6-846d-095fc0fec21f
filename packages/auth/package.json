{"name": "@moneta/auth", "version": "0.0.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./server": {"types": "./dist/server.d.ts", "import": "./dist/server.js"}, "./client": {"types": "./dist/client.d.ts", "import": "./dist/client.js"}, "./guards": {"types": "./dist/guards.d.ts", "import": "./dist/guards.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit"}, "dependencies": {"better-auth": "^1.2.10", "@tanstack/react-router": "^1.121.21", "@moneta/database": "workspace:*"}, "devDependencies": {"typescript": "^5.8.2"}}